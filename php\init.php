<?php


    use App\Libraries\Hooks;
    use Noodlehaus\Config;
    use App\Libraries\CSRFProtection;
    use App\Libraries\Auth;
    use App\Libraries\Language;
    use Illuminate\Database\Capsule\Manager as Capsule;
  
    // Define application root path
    define('APP_PATH', realpath(__DIR__ . '/..'));
    
	date_default_timezone_set('Asia/Muscat');
	ini_set('session.gc_maxlifetime', "74000");
	ini_set("session.cookie_lifetime","74000");
	session_start();

	$mode=file_get_contents(__DIR__ . '/mode.php');

	require_once (__DIR__ . '/../vendor/autoload.php');

	$conf=Config::load(__DIR__ . '/config/' . $mode .'.php');

	error_reporting(E_ALL);

	if (1){
	    ini_set('display_errors', 'On');        
	}
	else{
	    ini_set('display_errors', 'Off');
	}

	foreach (glob(__DIR__."/Libraries/*.php") as $lib)
	{
	    require_once $lib;
	}

	$_hooks = new Hooks();


	if (!function_exists('hooks')) {
		function hooks()
		{
			global $_hooks;
			return $_hooks;
		}
	}


	$capsule = new Capsule;

	$capsule->addConnection([
	    'driver'    => $conf->get('db.driver'),
	    'host'      => $conf->get('db.host'),
	    'database'  => $conf->get('db.database'),
	    'username'  => $conf->get('db.username'),
	    'password'  => $conf->get('db.password'),
	    'charset'   => 'utf8',
	    'collation' => 'utf8_unicode_ci',
	    'prefix'    => '',
	]);

	// Make this Capsule instance available globally via static methods... (optional)
	$capsule->setAsGlobal();

	// Setup the Eloquent ORM... (optional; unless you've used setEventDispatcher())
	$capsule->bootEloquent();

	foreach (glob(__DIR__."/Modals/*.php") as $modal)
	{
	    require_once $modal;
	}



	require_once(__DIR__ . '/classes/phonetics.class.php');



	require_once(__DIR__ . '/functions/file_save.function.php');
	require_once(__DIR__ . '/functions/file_save_multi.function.php');
	require_once(__DIR__ . '/functions/file_input.function.php');
	require_once(__DIR__ . '/functions/file_get.function.php');
	require_once(__DIR__ . '/functions/file_remove.function.php');



	

	foreach (glob(__DIR__."/Helpers/*.php") as $helper)
	{
	    require_once $helper;
	}

	


	$_language = new Language($use_cookies = true, $untranslated_logging = false);


	$csrf = new CSRFProtection();

	header(conf('csrf.session').': '.$csrf->get());

	$auth = new Auth();



	$db = new PDO\DB(); 




	$app_url=conf("App.url");


	if (isset($_GET['logout']) && $_GET['logout']==1) {
		$auth->logout();
	}
	


	//lang switch
if (isset($_GET['lang']) && !empty($_GET['lang']) && strlen($_GET['lang'])) {
  $_language->setLanguage($_GET['lang']);
}



$lang=$_language->lang;


	if ($lang=='AR') {$isRtl=true;}
	else{$isRtl=false;}

	if ($isRtl) {
		$local='lang="ar" dir="rtl"';

	}
	else{
		$local='lang="en" dir="ltr"';
	}


if (!is_cli()) {
	// code...

	$auth_page=[
	  	base_url() . 'index.php',
	  	base_url() . '',
	  	base_url() . '/',
	  	base_url() . 'resetpassword.php',
	];

	$ex_pages=[
		base_url() . 'ajax/is_active.php',
		base_url() . 'uploads/index.php',
		base_url() . 'uploads/',
		base_url() . 'fix_redirect.php',
	];



	if (auth()) {
		 
		if (in_array(url()->get(), $auth_page)) {
			if (isset($_GET['url']) && !empty($_GET['url'])) {
		      redirect()->to($_GET['url']);
		    }
	    
	    	redirect()->to(conf('App.url') . '/dashboard.php');
		}

	}else{

		if (!in_array(url()->get(), $auth_page)  ) {

			if (!in_array(url()->get(), $ex_pages)) {
				// Fix for the authentication redirect loop
				$current_url = url()->get();
				$redirect_url = isset($_GET['url']) ? $_GET['url'] : '';
				
				// Only redirect if we're not already in a loop
				if ($current_url != conf('App.url') . '/index.php' || $redirect_url != $current_url) {
					redirect()->to(conf('App.url') . '/index.php'.'?url='.urlencode($current_url));
				} else {
					// We detected a potential loop, so show an error page instead
					echo "<h1>Redirect Loop Detected</h1>";
					echo "<p>Please clear your cookies and try again.</p>";
					echo "<p><a href='fix_redirect.php'>Click here to fix automatically</a></p>";
					exit;
				}
			}
		}
	}




	branch();




}

require_once(__DIR__. DIRECTORY_SEPARATOR . "vars.php");




hooks()->do("init");






