
<form id="logout-form"  method="POST" style="display: none;">
    <input type="hidden" name="signout" value="1">
</form>
<div class="navbar navbar-expand-md bg-primary navbar-dark">

  <!-- Header with logos -->
  <div class="navbar-header navbar-dark d-none d-md-flex align-items-md-center bg-primary">
    <div class="navbar-brand navbar-brand-md ">
      <a href="<?= base_url() ?>" class="d-inline-block">
        <?= get_option("app_name") ?>
      </a>
    </div>
    
    <div class="navbar-brand navbar-brand-xs p-1">
      <a href="<?= base_url() ?>" class="d-inline-block">
        <?= get_option("app_name") ?>
      </a>
    </div>
  </div>
  <!-- /header with logos -->


  <!-- Mobile controls -->
  <div class="d-flex flex-1 d-md-none">
    <div class="navbar-brand mr-auto">
      <a href="<?= base_url() ?>" class="d-inline-block">
      <?= get_option("app_name") ?>
        
        
      </a>
    </div>	

    <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbar-mobile">
      <i class="icon-tree5"></i>
    </button>

    <button class="navbar-toggler sidebar-mobile-main-toggle" type="button">
      <i class="icon-paragraph-justify3"></i>
    </button>
  </div>
  <!-- /mobile controls -->


  <!-- Navbar content -->
  <div class="collapse navbar-collapse" id="navbar-mobile">
  <ul class="navbar-nav">
        <li class="nav-item">
          <a href="#" class="navbar-nav-link sidebar-control sidebar-main-toggle d-none d-md-block ">
            <i class="icon-paragraph-justify3"></i>
          </a>
        </li>

        <li class="nav-item dropdown">
        

          <div class="dropdown-menu dropdown-content wmin-md-350">
        

            <div class="dropdown-content-body dropdown-scrollable">
              <ul class="media-list">
           
                

   



              </ul>
            </div>

          
          </div>
        </li>
      </ul>

 

    <ul class="navbar-nav ml-auto">
      <li class="nav-item ">
        <a href="<?= base_url() ?>" class="navbar-nav-link " title="<?= tr("Home") ?>">
         <i class="fas fa-home"></i>
        </a>
      </li>
      <li class="nav-item ">
        <a href="<?= base_url() ?>/sales/pos.php"  class="navbar-nav-link " title="<?= tr("Point of sale") ?>">
         <i class="fas fa-shopping-basket"></i>
        </a>
      </li>
  
    
      
      <li class="nav-item dropdown">
        <a href="#" class="navbar-nav-link dropdown-toggle" data-toggle="dropdown">
          <img src="<?= base_url() ?>assets2/images/lang/<?= $_language->lang=="AR"?"ua":"gb" ?>.png" class="img-flag mr-2" alt="">
         <?= $_language->lang=="AR"?tr("Arabic"):tr("English") ?>
        </a>

        <div class="dropdown-menu dropdown-menu-right">
          <a href="<?= url()->add("lang","en") ?>" class="dropdown-item english <?= $_language->lang=="EN"?'active':'' ?>"><img src="<?= base_url() ?>assets2/images/lang/gb.png" class="img-flag" alt=""> <?= tr("English") ?></a>
          <a href="<?= url()->add("lang","ar") ?>" class="dropdown-item ukrainian <?= $_language->lang=="AR"?'active':'' ?>"><img src="<?= base_url() ?>assets2/images/lang/ua.png" class="img-flag" alt=""> <?= tr("Arabic") ?></a>
  
        </div>
      </li>

      <li class="nav-item dropdown dropdown-user">
        <a href="#" class="navbar-nav-link d-flex align-items-center dropdown-toggle" data-toggle="dropdown">
          <i class="fas fa-user px-2"></i>
        <span><?= escap(auth()->name) ?></span>
        </a>

        <div class="dropdown-menu dropdown-menu-right">
    

          <a href="<?= base_url() ?>profile/" class="dropdown-item" ><i class="far fa-user"></i> <?= tr("Profile") ?></a>
          <div class="dropdown-divider"></div>
        
          <a href="<?= base_url() ?>?logout=1" class="dropdown-item" ><i class="icon-switch2"></i> <?= tr("Logout") ?></a>
        </div>
      </li>
    </ul>
  </div>
  <!-- /navbar content -->
  
</div>

<div class="page-content">



<?php require_once(__DIR__."/sidebar.inc.php") ?>
  

	<div class="content-wrapper">

    <!-- Page header -->
<div class="page-header">

    <div class="page-header-content header-elements-sm-inline">
        <?php if (isset($page_nav)): ?>
          <?php if (isset($page_nav['breadcrumb'])): ?>
              <div class="page-title d-flex">
            <h4> 

   
              
                <?php $n=1; foreach ($page_nav['breadcrumb'] as $key => $value): ?>
                <?php if ($n!=count($page_nav['breadcrumb'])): ?>
                <a href="<?= $value ?>"><span class="font-weight-semibold"><?= tr($key) ?></span></a> /
                <?php else: ?>
                <span class="font-weight-semibold"><?= tr($key) ?></span>
                <?php endif ?>
                
                     
                <?php $n++; endforeach ?>  </h4>
                <a href="#" class="header-elements-toggle text-default d-sm-none"><i class="icon-more"></i></a>
            </div>
          <?php endif ?>
          
       
      
    
        

     

        <div class="header-elements d-none text-center text-sm-left mb-3 mb-sm-0">

          <?php if (isset($page_nav['branch'])): ?>
                 
              
            <button type="button" class="btn mx-1 bg-teal" data-toggle="dropdown" aria-expanded="true"><i class="fas fa-store-alt"></i> <?= tr("Warehouse") ?> (<?= branch()->name() ?>)</button>
                 
              <div class="dropdown-menu  " x-placement="bottom-end" style="position: absolute; will-change: transform; top: 0px; left: 0px; transform: translate3d(-16px, 36px, 0px);">
                        <?php foreach (branch()->list() as $key => $branch): ?>
                          <a href="<?= url()->add("branch",$branch['id']) ?>" class="dropdown-item"><i class="icon-menu7"></i> <?= escap($branch['name']) ?></a>
                        <?php endforeach ?>
                        
              
  
            </div>
                
                  
          <?php endif ?>

          <?php if (isset($page_nav['date_filter'])): ?>
          
              <button type="buttom" data-toggle="modal" data-target="#filter-modal" class="btn mx-1 bg-primary ">
                <i class="far fa-calendar-alt"></i> 
                <?= _date($page_nav['date_filter']['from']) ?> - <?= _date($page_nav['date_filter']['to']) ?>
              </button>


              <div class="modal" id="filter-modal" tabindex="-1" role="dialog" aria-labelledby="filter-modal-title" aria-hidden="true" data-backdrop="static" data-keyboard="false">
  <div class="modal-dialog modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h3 class="modal-title" id="filter-modal-title"><i class="far fa-calendar-alt"></i> <?= tr("Filter") ?></h3>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
        <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">

        
        <div class="row justify-content-center" >
        


          <div class="col-6 py-1">
            <button 
          class="btn-block text-center border btn btn-light" 
          onclick="$('#from').val('<?= date("Y-m-d") ?>');$('#to').val('<?= date("Y-m-d") ?>');$('#filter-form').submit();"
          ><?= tr("Today") ?></button>
        </div>
          <div class="col-6 py-1">
            <button 
          class="btn-block text-center border btn btn-light" 
          onclick="$('#from').val('<?= date("Y-m-d",strtotime("-1 day")) ?>',);$('#to').val('<?= date("Y-m-d",strtotime("-1 day")) ?>');$('#filter-form').submit();"
          ><?= tr("Yesterday") ?></button>
        </div>
          <div class="col-6 py-1">
            <button 
          class="btn-block text-center border btn btn-light" 
          onclick="
          $('#from').val('<?= date("Y-m-d",strtotime("-1 week")) ?>');
          $('#to').val('<?= date("Y-m-d") ?>');
          $('#filter-form').submit();"
          ><?= tr("Last Week") ?></button>
        </div>
          <div class="col-6 py-1">
            <button 
          class="btn-block text-center border btn btn-light" 
          onclick="$('#from').val('<?= date("Y-m-01") ?>');$('#to').val('<?= date("Y-m-t") ?>');$('#filter-form').submit();"
          ><?= tr("This Month") ?></button>
        </div>
          <div class="col-6 py-1">
            <button 
          class="btn-block text-center border btn btn-light" 
          onclick="$('#from').val('<?= date("Y-m-01",strtotime("-1 month")) ?>');$('#to').val('<?= date("Y-m-t",strtotime("-1 month")) ?>');$('#filter-form').submit();"
          ><?= tr("Last Month") ?></button>
        </div>
          <div class="col-6 py-1">
            <button 
          class="btn-block text-center border btn btn-light"
          onclick="$('#from').val('<?= date("Y-01-01") ?>');$('#to').val('<?= date("Y-12-t") ?>');$('#filter-form').submit();"
          ><?= tr("This Year") ?></button>
        </div>
          <div class="col-6 py-1">
            <button 
          class="btn-block text-center border btn btn-light"
          onclick="$('#from').val('<?= date("Y-01-01",strtotime("-1 year")) ?>');$('#to').val('<?= date("Y-12-t",strtotime("-1 year")) ?>');$('#filter-form').submit();"
          ><?= tr("Last Year") ?></button>
        </div>

          

        </div>

        <div class="row">
          <div class="col-6 py-1">
            <button 
          class="btn btn-light btn-block"
          onclick="$('#from').val('<?= date("Y-01-01") ?>');$('#to').val('<?= date("Y-03-31") ?>');$('#filter-form').submit();"
          ><?= tr("Q1") ?></button>

          </div>

          <div class="col-6 py-1">
            <button 
          class="btn btn-light btn-block"
          onclick="$('#from').val('<?= date("Y-04-01") ?>');$('#to').val('<?= date("Y-06-30") ?>');$('#filter-form').submit();"
          ><?= tr("Q2") ?></button>

          </div>

          <div class="col-6 py-1">
            <button 
          class="btn btn-light btn-block"
          onclick="$('#from').val('<?= date("Y-07-01") ?>');$('#to').val('<?= date("Y-09-30") ?>');$('#filter-form').submit();"
          ><?= tr("Q3") ?></button>

          </div>

          <div class="col-6 py-1">
            <button 
          class="btn btn-light btn-block"
          onclick="$('#from').val('<?= date("Y-10-01") ?>');$('#to').val('<?= date("Y-12-31") ?>');$('#filter-form').submit();"
          ><?= tr("Q4") ?></button>

          </div>
        </div>
      
        <hr>
        <?= tr("Date") ?>

        <form action="<?= url()->get() ?>" method="get" class="form-row mb-2 no_csrf" id="filter-form">
          <div class="col">
            <label for=""><?= tr("From") ?></label>
            <input  name="from" id="from" type="date" class="form-control text-center" value="<?= $page_nav['date_filter']['from'] ?>">
          </div>
          <div class="col">
            <label for=""><?= tr("To") ?></label>
            <input name="to" id="to"  type="date" class="form-control text-center" value="<?=$page_nav['date_filter']['to'] ?>">
          </div>



          <?php if (isset($_GET['i'])): ?>
            <input name="i" type="hidden" value="<?= $_GET['i'] ?>">
          <?php endif ?>
          <?php if (isset($_GET['v'])): ?>
            <input name="v" type="hidden" value="<?= $_GET['v'] ?>">
          <?php endif ?>
     
        </form>
 

      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-danger"  data-dismiss="modal"><?= tr("Cancel") ?></button>

        <button onclick="$('#filter-form').submit();" type="button" class="btn btn-primary"><?= tr("Apply") ?></button>
      </div>
    </div>
  </div>
</div>


          
          <?php endif ?>
          <?php if (isset($page_nav['reload'])): ?>
                 
              <button type="button" onclick="window.location.replace('<?= url()->get() ?>')" class="btn mx-1 bg-success">
                
                <?= tr("Reload") ?> <i class="fas fa-sync-alt"></i>
              </button>
                  
          <?php endif ?>
          <?php if (isset($page_nav['back'])): ?>
           <a href="<?= $page_nav['back'] ?>" class="btn bg-secondary mx-1 "> <?= tr("Back") ?> <i class="fas fa-arrow-<?= ($_language->lang=="AR")?'left':'right' ?>"></i></a>
           <?php endif ?>
           <?php if (isset($page_nav['close'])||isset($_GET['popup'])): ?>
            
              <button onclick="window.close()" type="button" class="btn bg-danger mx-1 btn-labeled btn-labeled-right rounded-round"><b><i class="fas fa-times"></i></b><?= tr("Close") ?></button>
            
            <?php endif ?>

          <?php if (isset($page_nav['buttons'])): ?>
     
          <div class="btn-group">
                  <a href="<?= $page_nav['buttons'][0]['url'] ?>" class="btn bg-indigo-400 rounded-round rounded-right-0"> <span class="mr-1"><?= $page_nav['buttons'][0]['icon'] ?></span> <?= $page_nav['buttons'][0]['title'] ?></a>
                            <button type="button" class="btn bg-indigo-400 rounded-round rounded-left-0 dropdown-toggle" data-toggle="dropdown" aria-expanded="false"></button>
                            <div class="dropdown-menu dropdown-menu-right" x-placement="bottom-end" style="position: absolute; transform: translate3d(-17px, 35px, 0px); top: 0px; left: 0px; will-change: transform;">
                        <?php foreach ($page_nav['buttons'] as $key => $button): ?>
                              <a href="<?= $button['url'] ?>" class="dropdown-item"><?= $button['icon'] ?> <?= $button['title'] ?></a>

                        <?php endforeach ?>
                  </div>
                </div>
  
          <?php endif ?>
           
         
            
        </div>
        <?php if ((isset($page_nav['close'])||isset($_GET['popup'])) && !isset($_GET['noreload'])): ?>
        <script type="text/javascript">
              function RefreshParent() {
                  if (window.opener != null && !window.opener.closed) {
                      window.opener.location.reload();
                  }
              }
              window.onbeforeunload = RefreshParent;
          </script>
      <?php endif ?>
        <?php endif ?>
    </div>
</div>
<!-- /page header -->

		<div class="content pt-0">