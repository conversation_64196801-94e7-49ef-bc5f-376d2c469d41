<?php

namespace App\Libraries;

use <PERSON>odlehaus\Config;
use Dotenv\Dotenv;

/**
 * Enhanced Configuration Class with .env Support
 * 
 * This class extends Noodlehaus Config to add .env file support while maintaining
 * full backward compatibility with existing PHP configuration files.
 * 
 * Priority order:
 * 1. .env file values (highest priority)
 * 2. PHP configuration files (fallback)
 * 3. Default values (lowest priority)
 */
class EnvConfig extends Config
{
    /**
     * Environment variables loaded from .env file
     * @var array
     */
    protected $envData = [];

    /**
     * Mapping of configuration keys to environment variable names
     * @var array
     */
    protected $envMapping = [
        // App configuration
        'App.url' => 'APP_URL',
        'App.name' => 'APP_NAME',
        'App.name_ar' => 'APP_NAME_AR',
        'App.logo' => 'APP_LOGO',
        'App.upload_path' => 'APP_UPLOAD_PATH',
        'App.mpdf' => 'APP_MPDF_PATH',
        'App.hash.algo' => 'APP_HASH_ALGO',
        'App.hash.cost' => 'APP_HASH_COST',
        
        // Database configuration
        'db.driver' => 'DB_DRIVER',
        'db.host' => 'DB_HOST',
        'db.port' => 'DB_PORT',
        'db.database' => 'DB_DATABASE',
        'db.username' => 'DB_USERNAME',
        'db.password' => 'DB_PASSWORD',
        'db.charset' => 'DB_CHARSET',
        'db.collation' => 'DB_COLLATION',
        'db.prefix' => 'DB_PREFIX',
        'db.backup_dir' => 'DB_BACKUP_DIR',
        'db.keep_files' => 'DB_KEEP_FILES',
        'db.compression' => 'DB_COMPRESSION',
        'db.extra_config' => 'DB_EXTRA_CONFIG',
        'db.protocol' => 'DB_PROTOCOL',
        'db.mysqldump' => 'DB_MYSQLDUMP',
        
        // Authentication configuration
        'auth.session' => 'AUTH_SESSION',
        'auth.remember' => 'AUTH_REMEMBER',
        'auth.remember_days' => 'AUTH_REMEMBER_DAYS',
        'auth.validate_ip' => 'AUTH_VALIDATE_IP',
        
        // CSRF configuration
        'csrf.session' => 'CSRF_SESSION',
        
        // Display configuration
        'display.DEBUG' => 'DISPLAY_DEBUG',
        
        // Upload configuration
        'upload.max_size' => 'UPLOAD_MAX_SIZE',
    ];

    /**
     * Constructor
     * 
     * @param string|array $values Filenames or string with configuration
     * @param ParserInterface $parser Configuration parser
     * @param bool $string Enable loading from string
     */
    public function __construct($values, $parser = null, $string = false)
    {
        // Load .env file if it exists
        $this->loadEnvFile();
        
        // Call parent constructor to load PHP config files
        parent::__construct($values, $parser, $string);
    }

    /**
     * Load .env file if it exists
     */
    protected function loadEnvFile()
    {
        $envPath = realpath(__DIR__ . '/../../');

        if (file_exists($envPath . '/.env')) {
            try {
                $dotenv = Dotenv::createImmutable($envPath);
                $dotenv->load();

                // Store environment variables for quick access
                foreach ($this->envMapping as $configKey => $envKey) {
                    if (isset($_ENV[$envKey])) {
                        $this->envData[$configKey] = $this->parseEnvValue($_ENV[$envKey]);
                    }
                }
            } catch (\Exception $e) {
                // For debugging: uncomment the line below to see errors
                // error_log("EnvConfig error: " . $e->getMessage());
                // Silently fail if .env file cannot be loaded
                // This ensures backward compatibility
            }
        }
    }

    /**
     * Parse environment variable value to appropriate type
     * 
     * @param string $value
     * @return mixed
     */
    protected function parseEnvValue($value)
    {
        // Handle boolean values
        if (strtolower($value) === 'true') {
            return true;
        }
        if (strtolower($value) === 'false') {
            return false;
        }
        
        // Handle null values
        if (strtolower($value) === 'null') {
            return null;
        }
        
        // Handle numeric values
        if (is_numeric($value)) {
            return strpos($value, '.') !== false ? (float) $value : (int) $value;
        }
        
        // Handle arrays (comma-separated values)
        if (strpos($value, ',') !== false) {
            return array_map('trim', explode(',', $value));
        }
        
        // Return as string
        return $value;
    }

    /**
     * Get configuration value with .env priority
     * 
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public function get($key, $default = null)
    {
        // First check if we have this value from .env file
        if (array_key_exists($key, $this->envData)) {
            return $this->envData[$key];
        }
        
        // Fall back to parent implementation (PHP config files)
        return parent::get($key, $default);
    }

    /**
     * Check if configuration key exists in .env or PHP config
     * 
     * @param string $key
     * @return bool
     */
    public function has($key)
    {
        // Check .env data first
        if (array_key_exists($key, $this->envData)) {
            return true;
        }
        
        // Fall back to parent implementation
        return parent::has($key);
    }

    /**
     * Set configuration value
     * Note: This will only affect the runtime configuration, not the .env file
     * 
     * @param string $key
     * @param mixed $value
     */
    public function set($key, $value)
    {
        // Update env data if this key is mapped
        if (array_key_exists($key, $this->envMapping)) {
            $this->envData[$key] = $value;
        }
        
        // Also update parent data
        parent::set($key, $value);
    }

    /**
     * Get all configuration data (merged .env and PHP config)
     * 
     * @return array
     */
    public function all()
    {
        $phpConfig = parent::all();
        
        // Merge with .env data, giving .env priority
        return $this->mergeConfigArrays($phpConfig, $this->envData);
    }

    /**
     * Merge configuration arrays with proper nested key handling
     * 
     * @param array $base
     * @param array $override
     * @return array
     */
    protected function mergeConfigArrays($base, $override)
    {
        foreach ($override as $key => $value) {
            $this->setNestedValue($base, $key, $value);
        }
        
        return $base;
    }

    /**
     * Set nested array value using dot notation
     * 
     * @param array &$array
     * @param string $key
     * @param mixed $value
     */
    protected function setNestedValue(&$array, $key, $value)
    {
        $keys = explode('.', $key);
        $current = &$array;
        
        foreach ($keys as $k) {
            if (!isset($current[$k]) || !is_array($current[$k])) {
                $current[$k] = [];
            }
            $current = &$current[$k];
        }
        
        $current = $value;
    }

    /**
     * Get the environment variable mapping
     * 
     * @return array
     */
    public function getEnvMapping()
    {
        return $this->envMapping;
    }

    /**
     * Get loaded environment data
     * 
     * @return array
     */
    public function getEnvData()
    {
        return $this->envData;
    }
}
