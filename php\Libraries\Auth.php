<?php
namespace App\Libraries;

use App\Libraries\Cookie;
use App\Libraries\Session;

use PDO\DB AS Database;
use Illuminate\Database\Capsule\Manager as DB;
use App\Models\User;

class Auth
{
	private     $user_name = "";
	private     $now       =""; 
	static     $db         =""; 
	private     $redirect  =""; 
	private     $error     =false; 
	public		$is_signed = false;
	public      $user      =[];
	public  static    $id =0;
	public  static    $ip        ='0';


	function __construct()
	{
	


		$db = new Database;



		if (self::auth()) {

			self::$id          =session()->get(conf('auth.session'));

		

			// clear user signs 
            $signs_count=$db->single("SELECT count(*) FROM users_signs WHERE user_id = ?",[self::$id ]);
          
	        if ($signs_count>150) {
	        	$db->query("DELETE FROM users_signs WHERE user_id =? LIMIT 40",[self::$id]);
	        }  
        
		}

	

		if (!self::auth()) {
			$this->loginWithRemember();
		}
	
		if ( isset($_POST["login"])) {
		
        	
        		$this->login();
        	

		}

		
		if (isset($_POST["logout"])) {

			if(isset($_SESSION)){
	            $_SESSION = array();
				session_regenerate_id();

				if (cookie()->get(conf("auth.remember"))) {
					$remember = explode('___', cookie()->get(conf("auth.remember")));

			    	if (count($remember)==2) {
			    		
			    		$remember_id    =$remember[0];
						$remember_token =$remember[1];

						DB::update("UPDATE user_remember SET status = 'Deleted' WHERE remember_id=?",[$remember_id]);
			    	}

			    	cookie()->unset(conf("auth.remember"));
				}

			}	

     	}

     

   
	}

	public function logout(){


		if(isset($_SESSION)){
			$_SESSION = array();
			session_regenerate_id();

			if (cookie()->get(conf("auth.remember"))) {
				$remember = explode('___', cookie()->get(conf("auth.remember")));

				if (count($remember)==2) {
					
					$remember_id    =$remember[0];
					$remember_token =$remember[1];

					DB::update("UPDATE user_remember SET status = 'Deleted' WHERE remember_id=?",[$remember_id]);
				}

				cookie()->unset(conf("auth.remember"));
			}

		}
		header("Location: ".conf("App.url"));
		exit;
	}


	private function loginWithRemember(){

		if (cookie()->get(conf("auth.remember"))==null) {
			return false;
		}

		$remember = explode('___', cookie()->get(conf("auth.remember")));

    	if (count($remember)!=2) {
    		cookie()->unset(conf("auth.remember"));
    		return false;
    	}

		$remember_id    =$remember[0];
		$remember_token =$remember[1];

		$remember_row = DB::table("user_remember");
		$remember_row = $remember_row->where("remember_id",$remember_id);
		$remember_row = $remember_row->where("status",'Active');

		if (conf("auth.validate_ip")) {
			$remember_row = $remember_row->where("ip",_ip());
		}

		$remember_row = $remember_row->first();
	
    	if (!$remember_row) {
    		return false;
    	}

    	// var_dump($remember_row);

		$user = User::where("id",$remember_row->user_id)->first();
		
		if (!$user) {
			cookie()->unset(conf("auth.remember"));
    		return false;
    	}

    	if (password_verify($remember_token,$remember_row->remember_token)) {

         	
            session()->set(conf('auth.session'),$user->id);

            DB::insert("INSERT INTO user_session(session_id,ip) VALUES(?,?)",[session_id(),_ip()]);
            self::$id=$user->id;

			DB::table("users_signs")->insert(["user_id"=>$user->id,"ip"=>_ip()]); 

			

    	}else{
    		// DB::update("UPDATE user_remember SET status='Deleted' WHERE user_id = ?",[$user->id]);
    		cookie()->unset(conf("auth.remember"));
    		return false;
    	}
    	
    }



	private function doLogout() {
		if(isset($_SESSION)){
            $_SESSION = array();
			session_regenerate_id();

			if (cookie()->get(conf("auth.remember"))) {
				$remember = explode('___', cookie()->get(conf("auth.remember")));

		    	if (count($remember)==2) {
		    		
		    		$remember_id    =$remember[0];
					$remember_token =$remember[1];

					DB::update("UPDATE user_remember SET status = 'Deleted' WHERE remember_id=?",[$remember_id]);
		    	}

		    	cookie()->unset(conf("auth.remember"));
			}

		}
  	
        redirect()->to(conf("App.url"));		
    }

    private function login() { // Login with the form content



    	$v = validate([
			tr('Username')=> ["username",'required'],
			tr('Password')=> ["password",'required'],
		]);


		
		$recaptcha_status=true;



		
		if (!$v->passes()) {

		
			return _response([
				"success"=>false,
				"message"=>$v->errors()->all()
			]);

		}
		

		$user = User::where(function ($query)  {
		    $query->where('phone', input('username'))
		          ->orWhere('log_name', input('username'))
		          ->orWhere('email', input('username'));
		})->first();

		if (!$user) {
			
			return _response([
				"success"=>false,
				"message"=>[tr("Invalid credentials")]
			]);
		}

		if (!password_verify(input('password'),$user->password)) {

			return _response([
				"success"=>false,
				"message"=>[tr("Invalid credentials")]
			]);
		}

			
		
			
		DB::insert("INSERT INTO user_session(session_id,ip) VALUES(?,?)",[session_id(),_ip()]);

        session()->set(conf('auth.session'),$user->id);

        self::$id=$user->id;

			DB::table("users_signs")->insert(["user_id"=>$user->id,"ip"=>_ip()]); 

			 


		if (input("remember_me")) { // remember me

			$remember_token=bin2hex(random_bytes(50).time());
			$remember_id=bin2hex(random_bytes(80).time());

			if (cookie()->get(conf("auth.remember"))) {

				$remember = explode('___', cookie()->get(conf("auth.remember")));

		    	if (count($remember)!=2) {
		    		cookie()->unset(conf("auth.remember"));
		    
		    	}else{
		    		$remember_id    =$remember[0];
					$remember_token =$remember[1];

					DB::update("UPDATE user_remember SET status='Deleted' WHERE  remember_id = ?",[$remember_id]);
					cookie()->unset(conf("auth.remember"));
		    	}

			}

			DB::table("user_remember")->insert(
				[
					"user_id"=>$user->id,
					"remember_token"=>password_hash($remember_token, PASSWORD_DEFAULT),
					"remember_id"=>$remember_id,
					"ip"=>_ip(),
				]
			); 

			cookie()->set(
				conf("auth.remember"), 
				$remember_id."___".$remember_token, 
				conf("auth.remember_days")
			);

		}

		


        return _response([
			"success"=>true,
			"action"=>'reload'
		]);
         
			
    
	}


	 public static function isSigned(){


		if (isset($_SESSION[conf('auth.session')]) && $_SESSION[conf('auth.session')]>=1) {

			$session = DB::table('user_session')->where('session_id',session_id());
			if (conf("auth.validate_ip")) {
				$session = $session->where("ip",_ip());
			}
			$session = $session->first();

			

			if ($session) {
				$id = session()->get(conf('auth.session')); 
				$checklogin =User::find($id);
				if ($checklogin) {
					return true;
				}
			}
			
		}

		return false;
	}






	public function authorize($role){
		if (!can($role)) {
			die(403);
		}
	}

	

	static public function auth(){

		if (!self::isSigned()) {
			return false;
		}

		return User::find(session()->get(conf('auth.session')));
	}


}