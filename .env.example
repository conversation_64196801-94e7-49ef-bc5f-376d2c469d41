# StockV3 Environment Configuration
# Copy this file to .env and update the values according to your environment

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# Application URL (without trailing slash)
APP_URL=http://localhost/stockv3

# Application Name
APP_NAME="Stock Manager"
APP_NAME_AR="المخاسب"

# Application Logo filename (stored in uploads directory)
APP_LOGO=319531595685446.jpeg

# Upload Path (absolute path to uploads directory)
APP_UPLOAD_PATH="C:/xampp/htdocs/stockv3/uploads/"

# mPDF temporary directory path
APP_MPDF_PATH="C:/phptmp"

# Password Hashing Configuration
# Use PASSWORD_BCRYPT for algo (PHP constant value: 1)
APP_HASH_ALGO=1
APP_HASH_COST=10

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# Database Driver
DB_DRIVER=mysql

# Database Connection
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=app_stockv3
DB_USERNAME=root
DB_PASSWORD=

# Database Character Set
DB_CHARSET=utf8
DB_COLLATION=utf8_unicode_ci

# Database Table Prefix
DB_PREFIX=

# Database Backup Configuration
DB_BACKUP_DIR="C:/xampp/htdocs/stockv2/php/crons/"
DB_KEEP_FILES=60
DB_COMPRESSION=
DB_EXTRA_CONFIG=
DB_PROTOCOL=

# MySQL Dump Path
DB_MYSQLDUMP="C:/xampp/mysql/bin/mysqldump"

# =============================================================================
# AUTHENTICATION CONFIGURATION
# =============================================================================

# Session Configuration
AUTH_SESSION=user
AUTH_REMEMBER=user_r
AUTH_REMEMBER_DAYS=30

# IP Validation (true/false)
AUTH_VALIDATE_IP=true

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# CSRF Token Session Name
CSRF_SESSION=csrf_token

# =============================================================================
# DISPLAY CONFIGURATION
# =============================================================================

# Debug Mode (1 for enabled, 0 for disabled)
DISPLAY_DEBUG=1

# =============================================================================
# UPLOAD CONFIGURATION
# =============================================================================

# Maximum Upload Size (in bytes)
# 12582000 = 12MB
UPLOAD_MAX_SIZE=12582000

# Allowed File Extensions (comma-separated)
# Note: This is handled in PHP config for now, but can be moved to .env later
# UPLOAD_ALLOWED_EXT=png,jpg,gif,pdf,txt,jpeg,xlsx,xls

# =============================================================================
# ADDITIONAL CONFIGURATION
# =============================================================================

# Add any additional environment-specific configuration here
# These will override the corresponding PHP configuration values

# Example: Email Configuration (if needed)
# MAIL_HOST=smtp.example.com
# MAIL_PORT=587
# MAIL_USERNAME=<EMAIL>
# MAIL_PASSWORD=your-password
# MAIL_ENCRYPTION=tls

# Example: SMS Configuration (if needed)
# SMS_PROVIDER=twilio
# SMS_API_KEY=your-api-key
# SMS_API_SECRET=your-api-secret

# Example: Payment Gateway Configuration (if needed)
# PAYMENT_GATEWAY=stripe
# PAYMENT_PUBLIC_KEY=pk_test_...
# PAYMENT_SECRET_KEY=sk_test_...

# =============================================================================
# DEVELOPMENT/PRODUCTION SPECIFIC
# =============================================================================

# Environment Mode (development, production, testing)
APP_ENV=development

# Timezone
APP_TIMEZONE=Asia/Muscat

# =============================================================================
# NOTES
# =============================================================================
# 
# 1. Boolean values should be written as: true, false (lowercase)
# 2. Null values should be written as: null (lowercase)
# 3. Numeric values will be automatically converted to integers or floats
# 4. Comma-separated values will be converted to arrays
# 5. String values with spaces should be quoted
# 6. Backslashes in paths should be escaped (\\) or use forward slashes (/)
# 7. Do not add spaces around the = sign
# 8. Comments start with # and are ignored
# 
# Priority Order:
# 1. .env file values (highest priority)
# 2. PHP configuration files (fallback)
# 3. Default values in code (lowest priority)
#
# This means you can gradually migrate from PHP config to .env by adding
# values to this file. Any value not present in .env will fall back to
# the existing PHP configuration files.
