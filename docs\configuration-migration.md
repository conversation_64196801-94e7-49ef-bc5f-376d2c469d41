# Configuration Migration Guide: PHP Config to .env

## Overview

This guide documents the migration of StockV3's configuration system from PHP-based config files to .env file support while maintaining full backward compatibility.

## Migration Benefits

- **Environment-specific configuration**: Easy configuration management across different environments
- **Security**: Sensitive data (passwords, API keys) can be kept out of version control
- **Standardization**: Follows modern PHP application configuration patterns
- **Backward compatibility**: Existing PHP config files continue to work
- **Gradual migration**: Move configuration values incrementally

## How It Works

### Priority System

The new configuration system uses a three-tier priority system:

1. **`.env` file values** (highest priority)
2. **PHP configuration files** (fallback)
3. **Default values in code** (lowest priority)

### Implementation Details

- **EnvConfig Class**: Extends Noodlehaus Config to add .env support
- **Automatic Type Conversion**: Environment variables are automatically converted to appropriate PHP types
- **Dot Notation Support**: Maintains existing `conf('App.url')` syntax
- **Seamless Integration**: No changes required to existing `conf()` function calls

## Environment Variable Naming Convention

Environment variables follow a consistent naming pattern:

| Configuration Key | Environment Variable | Example Value |
|-------------------|---------------------|---------------|
| `App.url` | `APP_URL` | `http://localhost/stockv3` |
| `App.name` | `APP_NAME` | `"Stock Manager"` |
| `db.host` | `DB_HOST` | `127.0.0.1` |
| `db.database` | `DB_DATABASE` | `app_stockv3` |
| `auth.session` | `AUTH_SESSION` | `user` |
| `csrf.session` | `CSRF_SESSION` | `csrf_token` |

### Naming Rules

1. **Uppercase**: All environment variables use UPPERCASE
2. **Underscores**: Use underscores instead of dots
3. **Prefixes**: Group related settings with prefixes (APP_, DB_, AUTH_, etc.)
4. **Descriptive**: Names should be clear and descriptive

## Migration Steps

### Step 1: Copy Example File

```bash
cp .env.example .env
```

### Step 2: Update Values

Edit the `.env` file with your environment-specific values:

```env
# Application Configuration
APP_URL=http://your-domain.com/stockv3
APP_NAME="Your Stock System"

# Database Configuration
DB_HOST=your-db-host
DB_DATABASE=your-database-name
DB_USERNAME=your-username
DB_PASSWORD=your-password
```

### Step 3: Test Configuration

The system will automatically use .env values when available, falling back to PHP config files for any missing values.

## Data Type Handling

### Boolean Values
```env
# Use lowercase true/false
AUTH_VALIDATE_IP=true
DISPLAY_DEBUG=false
```

### Numeric Values
```env
# Integers and floats are automatically detected
DB_PORT=3306
APP_HASH_COST=10
UPLOAD_MAX_SIZE=12582000
```

### String Values with Spaces
```env
# Quote strings containing spaces
APP_NAME="Stock Management System"
APP_NAME_AR="نظام إدارة المخزون"
```

### Null Values
```env
# Use lowercase null
DB_PASSWORD=null
```

### Array Values
```env
# Comma-separated values become arrays
UPLOAD_ALLOWED_EXT=png,jpg,gif,pdf,txt,jpeg,xlsx,xls
```

## Security Considerations

### What to Include in .env
- Database credentials
- API keys and secrets
- Environment-specific URLs
- Debug settings
- File paths

### What NOT to Include in Version Control
- Never commit `.env` files to version control
- Use `.env.example` as a template
- Add `.env` to your `.gitignore` file

### Example .gitignore Entry
```gitignore
# Environment files
.env
.env.local
.env.production
```

## Backward Compatibility

### Existing Code Continues to Work
All existing `conf()` function calls work without modification:

```php
// These continue to work exactly as before
$app_url = conf('App.url');
$db_host = conf('db.host');
$session_name = conf('auth.session');
```

### PHP Config Files Still Active
- PHP configuration files in `php/config/` remain functional
- Values not found in `.env` automatically fall back to PHP config
- No need to migrate everything at once

### Gradual Migration Strategy
1. Start with sensitive data (database credentials, API keys)
2. Move environment-specific settings (URLs, paths)
3. Gradually migrate other configuration values
4. Keep PHP config files as fallback

## Configuration Mapping Reference

### Application Settings
```env
APP_URL=http://localhost/stockv3
APP_NAME="Stock Manager"
APP_NAME_AR="المخاسب"
APP_LOGO=319531595685446.jpeg
APP_UPLOAD_PATH="C:\xampp\htdocs\stockv3\uploads\\"
APP_MPDF_PATH="C:\phptmp"
APP_HASH_ALGO=1
APP_HASH_COST=10
```

### Database Settings
```env
DB_DRIVER=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=app_stockv3
DB_USERNAME=root
DB_PASSWORD=
DB_CHARSET=utf8
DB_COLLATION=utf8_unicode_ci
DB_PREFIX=
```

### Authentication Settings
```env
AUTH_SESSION=user
AUTH_REMEMBER=user_r
AUTH_REMEMBER_DAYS=30
AUTH_VALIDATE_IP=true
```

### Security Settings
```env
CSRF_SESSION=csrf_token
```

### Display Settings
```env
DISPLAY_DEBUG=1
```

### Upload Settings
```env
UPLOAD_MAX_SIZE=12582000
```

## Troubleshooting

### Common Issues

1. **Environment variables not loading**
   - Check `.env` file exists in project root
   - Verify file permissions
   - Check for syntax errors in `.env` file

2. **Boolean values not working**
   - Use lowercase: `true`, `false`, `null`
   - Avoid quotes around boolean values

3. **Paths with backslashes**
   - Escape backslashes: `C:\\xampp\\htdocs\\stockv3`
   - Or use forward slashes: `C:/xampp/htdocs/stockv3`

4. **Values with spaces**
   - Quote the entire value: `APP_NAME="My Application"`

### Debugging

To check which configuration source is being used:

```php
// Check if value comes from .env
$envConfig = new \App\Libraries\EnvConfig($configFile);
$envData = $envConfig->getEnvData();
var_dump($envData);

// Check all configuration
var_dump($envConfig->all());
```

## Best Practices

1. **Use descriptive variable names**
2. **Group related settings with prefixes**
3. **Document complex configurations**
4. **Keep sensitive data in .env only**
5. **Use .env.example for team coordination**
6. **Test configuration in different environments**
7. **Maintain backward compatibility during migration**

## Next Steps

1. Copy `.env.example` to `.env`
2. Update values for your environment
3. Test the application functionality
4. Gradually migrate more settings from PHP config to .env
5. Update deployment processes to handle .env files
6. Train team members on new configuration approach
